import React, { useEffect } from 'react';

// Performance optimizations for smooth UI
const PerformanceOptimizer: React.FC = () => {
  useEffect(() => {
    // Enable smooth scrolling
    if (typeof window !== 'undefined') {
      // Set scroll behavior for the entire page
      document.documentElement.style.scrollBehavior = 'smooth';
      
      // Optimize animations for better performance
      const style = document.createElement('style');
      style.textContent = `
        * {
          backface-visibility: hidden;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        
        body {
          scroll-behavior: smooth;
          overscroll-behavior: none;
        }
        
        .interactive {
          transform: translateZ(0);
        }
        
        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `;
      document.head.appendChild(style);

      // Optimize scroll performance
      let ticking = false;
      const onScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', onScroll, { passive: true });

      return () => {
        window.removeEventListener('scroll', onScroll);
        document.head.removeChild(style);
      };
    }
  }, []);

  return null;
};

export default PerformanceOptimizer;