import React, { useEffect, useRef } from 'react';

const CustomCursor: React.FC = () => {
  const cursorRef = useRef<HTMLDivElement>(null);
  const trailRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const cursor = cursorRef.current;
    if (!cursor) return;

    // Force initial visibility
    cursor.style.display = 'block';
    cursor.style.opacity = '1';
    cursor.style.visibility = 'visible';

    const moveCursor = (e: MouseEvent) => {
      // Use requestAnimationFrame for smooth movement
      requestAnimationFrame(() => {
        if (cursor) {
          cursor.style.left = e.clientX + 'px';
          cursor.style.top = e.clientY + 'px';
          cursor.style.opacity = '1';
          cursor.style.visibility = 'visible';
        }
      });
    };

    const handleMouseEnter = () => {
      cursor?.classList.add('hover');
    };

    const handleMouseLeave = () => {
      cursor?.classList.remove('hover');
    };

    const handleMouseOut = () => {
      // Keep cursor visible even when mouse leaves viewport
      if (cursor) {
        cursor.style.opacity = '0.5';
      }
    };

    const handleMouseOver = () => {
      // Make cursor fully visible when mouse re-enters
      if (cursor) {
        cursor.style.opacity = '1';
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', moveCursor, { passive: true });
    document.addEventListener('mouseout', handleMouseOut);
    document.addEventListener('mouseover', handleMouseOver);
    
    // Add hover effects to all interactive elements
    const addHoverListeners = () => {
      const interactiveElements = document.querySelectorAll('button, a, input, textarea, .interactive, [role="button"]');
      interactiveElements.forEach(el => {
        el.addEventListener('mouseenter', handleMouseEnter);
        el.addEventListener('mouseleave', handleMouseLeave);
      });
      return interactiveElements;
    };

    let interactiveElements = addHoverListeners();

    // Re-add listeners when new elements are added to DOM
    const observer = new MutationObserver(() => {
      interactiveElements = addHoverListeners();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => {
      document.removeEventListener('mousemove', moveCursor);
      document.removeEventListener('mouseout', handleMouseOut);
      document.removeEventListener('mouseover', handleMouseOver);
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
      observer.disconnect();
    };
  }, []);

  return (
    <div 
      ref={cursorRef} 
      className="custom-cursor"
      style={{
        position: 'fixed',
        zIndex: 99999,
        pointerEvents: 'none',
        display: 'block',
        opacity: 1,
        visibility: 'visible'
      }}
    />
  );
};

export default CustomCursor;