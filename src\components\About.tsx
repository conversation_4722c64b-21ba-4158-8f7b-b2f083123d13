import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
  FileHtml,
  FileCss,
  FileJs,
  Database,
  Code,
  ChartBar,
  Globe,
  Lightning
} from 'phosphor-react';

const About: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const skillsRef = useRef<HTMLDivElement>(null);

  const skills = [
    { name: 'HTML', icon: FileHtml },
    { name: 'CSS', icon: FileCss },
    { name: 'JavaScript', icon: FileJs },
    { name: 'React', icon: Code },
    { name: 'Python', icon: Code },
    { name: 'Java', icon: Code },
    { name: 'PHP', icon: Globe },
    { name: 'MySQL', icon: Database },
    { name: 'Data Analytics', icon: ChartBar },
    { name: 'Vibe Coding', icon: Lightning }
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Only animate if elements exist
      if (sectionRef.current) {
        gsap.fromTo(sectionRef.current,
          { opacity: 0 },
          {
            opacity: 1,
            duration: 1,
            scrollTrigger: {
              trigger: sectionRef.current,
              start: 'top 80%',
              toggleActions: 'play none none reverse'
            }
          }
        );
      }

      // Image animation
      gsap.fromTo(imageRef.current,
        { x: -100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: imageRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Content animation
      gsap.fromTo(contentRef.current,
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: contentRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Skills staggered animation
      gsap.fromTo('.skill-item',
        { scale: 0, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          ease: 'back.out(1.7)',
          stagger: 0.1,
          scrollTrigger: {
            trigger: skillsRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    return () => ctx.revert();
  }, []);

  return (
    <section
      id="about"
      ref={sectionRef}
      className="min-h-screen flex items-center px-6 md:px-12 lg:px-24 py-20"
    >
      <div className="w-full max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        {/* Profile Image */}
        <div ref={imageRef} className="flex justify-center lg:justify-start">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-700"></div>
            <div className="relative w-80 h-96 bg-gradient-to-br from-background/10 to-muted/20 rounded-xl overflow-hidden glass-card border-2 border-primary/20 p-3">
              <img
                src="/lovable-uploads/3e3b5507-cb25-48ff-99ac-db0b73db718b.png"
                alt="Nitesh Gupta - Developer"
                className="w-full h-full object-contain transform group-hover:scale-105 transition-all duration-700"
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div ref={contentRef} className="space-y-8">
          <div>
            <h2 className="text-4xl md:text-5xl font-light text-glow mb-6">
              About Me
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              I'm a passionate web developer with expertise in creating 
              immersive digital experiences. I specialize in modern frontend 
              technologies and love bringing creative visions to life through 
              clean, efficient code.
            </p>
          </div>

          {/* Skills Grid */}
          <div ref={skillsRef}>
            <h3 className="text-2xl font-medium mb-6 text-foreground/90">
              Technical Skills
            </h3>
            <div className="grid grid-cols-5 gap-4">
              {skills.map((skill, index) => {
                const IconComponent = skill.icon;
                return (
                  <div
                    key={skill.name}
                    className="skill-item skill-icon group"
                    title={skill.name}
                  >
                    <IconComponent 
                      size={24} 
                      className="text-foreground/80 group-hover:text-primary transition-colors duration-300"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* CTA */}
          <div>
            <button
              onClick={() => {
                const element = document.querySelector('#projects');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="btn-glass"
            >
              View My Work
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;