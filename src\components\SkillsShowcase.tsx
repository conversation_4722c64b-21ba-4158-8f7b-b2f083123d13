import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

const SkillsShowcase: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);

  const skills = [
    { name: 'React', level: 95, color: 'from-blue-500 to-cyan-500' },
    { name: 'TypeScript', level: 90, color: 'from-blue-600 to-blue-400' },
    { name: 'JavaScript', level: 98, color: 'from-yellow-500 to-orange-500' },
    { name: 'Python', level: 85, color: 'from-green-500 to-green-400' },
    { name: 'Node.js', level: 88, color: 'from-green-600 to-green-500' },
    { name: 'CSS/SCSS', level: 92, color: 'from-pink-500 to-purple-500' },
    { name: 'MongoDB', level: 80, color: 'from-green-600 to-green-400' },
    { name: '<PERSON><PERSON>', level: 88, color: 'from-purple-600 to-purple-400' },
    { name: 'MySQL', level: 85, color: 'from-orange-600 to-orange-400' },
    { name: 'Vibe Coding', level: 95, color: 'from-indigo-500 to-purple-500' },
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate skill bars
      skills.forEach((_, index) => {
        const bar = document.querySelector(`.skill-bar-${index}`);
        const percentage = document.querySelector(`.skill-percentage-${index}`);
        
        if (bar && percentage) {
          gsap.fromTo(bar,
            { width: '0%' },
            {
              width: `${skills[index].level}%`,
              duration: 1.5,
              ease: 'power2.out',
              scrollTrigger: {
                trigger: bar,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
              }
            }
          );

          gsap.fromTo(percentage,
            { innerHTML: '0%' },
            {
              innerHTML: `${skills[index].level}%`,
              duration: 1.5,
              ease: 'power2.out',
              scrollTrigger: {
                trigger: percentage,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
              }
            }
          );
        }
      });
    });

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="py-20 px-6 md:px-12 lg:px-24"
    >
      <div className="max-w-4xl mx-auto">
        <h2 className="text-4xl md:text-5xl font-light text-glow text-center mb-16">
          Technical Expertise
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {skills.map((skill, index) => (
            <div
              key={skill.name}
              className="glass-card group hover:glow transition-all duration-500"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-foreground">
                  {skill.name}
                </h3>
                <span className={`skill-percentage-${index} text-sm font-bold text-primary`}>
                  0%
                </span>
              </div>
              
              <div className="w-full bg-muted/20 rounded-full h-3 overflow-hidden">
                <div
                  className={`skill-bar-${index} h-full bg-gradient-to-r ${skill.color} rounded-full transition-all duration-300 relative`}
                  style={{ width: '0%' }}
                >
                  <div className="absolute inset-0 bg-white/20 animate-pulse rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SkillsShowcase;