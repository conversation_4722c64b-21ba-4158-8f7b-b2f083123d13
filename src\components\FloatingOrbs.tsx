import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const FloatingOrbs: React.FC = () => {
  const orbsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const orbs = orbsRef.current?.children;
    if (!orbs) return;

    // Simplified animation
    Array.from(orbs).forEach((orb, index) => {
      gsap.to(orb, {
        y: -20,
        duration: 3 + index,
        repeat: -1,
        yoyo: true,
        ease: 'power1.inOut',
        delay: index * 0.5
      });
    });
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      <div ref={orbsRef}>
        {/* Reduced number of orbs */}
        <div className="absolute w-32 h-32 top-1/4 left-1/4 rounded-full blur-xl opacity-20 bg-primary/10" />
        <div className="absolute w-24 h-24 bottom-1/4 right-1/4 rounded-full blur-xl opacity-15 bg-secondary/10" />
        <div className="absolute w-28 h-28 top-1/2 right-1/3 rounded-full blur-xl opacity-10 bg-accent/10" />
      </div>
    </div>
  );
};

export default FloatingOrbs;