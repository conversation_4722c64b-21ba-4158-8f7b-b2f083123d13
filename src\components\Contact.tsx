import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { GithubLogo, LinkedinLogo, PaperPlaneTilt } from 'phosphor-react';

const Contact: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: titleRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Form elements staggered animation
      gsap.fromTo('.form-element',
        { opacity: 0, x: -50 },
        {
          opacity: 1,
          x: 0,
          duration: 0.8,
          ease: 'power2.out',
          stagger: 0.2,
          scrollTrigger: {
            trigger: formRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Social icons animation
      gsap.fromTo('.social-icon',
        { scale: 0, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          ease: 'back.out(1.7)',
          stagger: 0.1,
          scrollTrigger: {
            trigger: '.social-icons',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    return () => ctx.revert();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Animate submit button
    const submitBtn = e.currentTarget.querySelector('.submit-btn');
    gsap.to(submitBtn, {
      scale: 1.1,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.out'
    });

    // Create mailto link for email client
    const emailSubject = `Portfolio Contact from ${formData.name}`;
    const emailBody = `Name: ${formData.name}%0A%0AEmail: ${formData.email}%0A%0AMessage:%0A${formData.message}`;
    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(emailSubject)}&body=${emailBody}`;
    
    // Open email client
    window.location.href = mailtoLink;
    
    // Show success animation
    gsap.to(submitBtn, {
      backgroundColor: '#10b981',
      duration: 0.3,
      ease: 'power2.out',
      onComplete: () => {
        setTimeout(() => {
          gsap.to(submitBtn, {
            backgroundColor: '',
            duration: 0.3
          });
        }, 2000);
      }
    });
    
    // Reset form after a delay
    setTimeout(() => {
      setFormData({ name: '', email: '', message: '' });
    }, 1000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section
      id="contact"
      ref={sectionRef}
      className="min-h-screen flex items-center px-6 md:px-12 lg:px-24 py-20"
      data-scroll-section
    >
      <div className="w-full max-w-4xl mx-auto">
        <h2
          ref={titleRef}
          className="text-4xl md:text-5xl font-light text-glow text-center mb-16"
        >
          Get In Touch
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <div className="space-y-8">
            <div className="form-element">
              <h3 className="text-2xl font-medium mb-4 text-foreground/90">
                Let's Work Together
              </h3>
              <p className="text-muted-foreground leading-relaxed mb-8">
                Have a project in mind or just want to say hello? Drop me a message
                and I'll get back to you as soon as possible.
              </p>
            </div>

            {/* Contact Details */}
            <div className="form-element space-y-4">
              <div className="glass-card">
                <h4 className="font-medium text-foreground/90 mb-2">Email</h4>
                <p className="text-muted-foreground"><EMAIL></p>
              </div>
              
              <div className="glass-card">
                <h4 className="font-medium text-foreground/90 mb-2">Name</h4>
                <p className="text-muted-foreground">Nitesh Gupta</p>
              </div>
            </div>

            {/* Social Links */}
            <div className="form-element social-icons">
              <h4 className="font-medium text-foreground/90 mb-4">Follow Me</h4>
              <div className="flex space-x-4">
                <a
                  href="https://github.com/nitesh124-coder"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-icon glass p-4 rounded-xl hover:glow transition-all duration-300"
                >
                  <GithubLogo size={24} className="text-foreground/80" />
                </a>
                
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-icon glass p-4 rounded-xl hover:glow transition-all duration-300"
                >
                  <LinkedinLogo size={24} className="text-foreground/80" />
                </a>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <form
            ref={formRef}
            onSubmit={handleSubmit}
            className="space-y-6"
          >
            <div className="form-element">
              <label htmlFor="name" className="block text-sm font-medium text-foreground/90 mb-2">
                Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="input-glass"
                placeholder="Your name"
              />
            </div>

            <div className="form-element">
              <label htmlFor="email" className="block text-sm font-medium text-foreground/90 mb-2">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="input-glass"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-element">
              <label htmlFor="message" className="block text-sm font-medium text-foreground/90 mb-2">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={5}
                className="input-glass resize-none"
                placeholder="Tell me about your project..."
              />
            </div>

            <div className="form-element">
              <button
                type="submit"
                className="submit-btn btn-primary w-full flex items-center justify-center space-x-2"
              >
                <span>Send Message</span>
                <PaperPlaneTilt size={20} />
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Contact;