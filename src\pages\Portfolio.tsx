import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Components
import Preloader from '../components/Preloader';
import Navigation from '../components/Navigation';
import Hero from '../components/Hero';
import About from '../components/About';
import SkillsShowcase from '../components/SkillsShowcase';
import OptimizedProjects from '../components/OptimizedProjects';
import Testimonials from '../components/Testimonials';
import Contact from '../components/Contact';
import Footer from '../components/Footer';
import FloatingOrbs from '../components/FloatingOrbs';
import CustomCursor from '../components/CustomCursor';
import ParticleField from '../components/ParticleField';
import PerformanceOptimizer from '../components/PerformanceOptimizer';
import ErrorBoundary from '../components/ErrorBoundary';
import SmoothScrollWrapper from '../components/SmoothScrollWrapper';

gsap.registerPlugin(ScrollTrigger);

const Portfolio: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Simple scroll behavior instead of Locomotive
    if (containerRef.current) {
      // Refresh ScrollTrigger on load
      ScrollTrigger.refresh();
    }

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <>
      <Preloader />
      <div 
        ref={containerRef}
        className="main-content relative overflow-hidden"
        style={{ opacity: 0 }}
      >
        <ErrorBoundary>
          <SmoothScrollWrapper>
            <PerformanceOptimizer />
            <CustomCursor />
            <ParticleField />
            <FloatingOrbs />
            <Navigation />
            
            <main>
              <Hero />
              <About />
              <div id="skills">
                <SkillsShowcase />
              </div>
              <OptimizedProjects />
              <div id="testimonials">
                <Testimonials />
              </div>
              <Contact />
            </main>
            
            <Footer />
          </SmoothScrollWrapper>
        </ErrorBoundary>
      </div>
    </>
  );
};

export default Portfolio;