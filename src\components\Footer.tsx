import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>in<PERSON><PERSON>, Heart } from 'phosphor-react';

const Footer: React.FC = () => {
  const footerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.fromTo(footerRef.current,
        { opacity: 0, y: 60, filter: 'blur(10px)' },
        {
          opacity: 1,
          y: 0,
          filter: 'blur(0px)',
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: footerRef.current,
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Floating particles animation
      gsap.to('.particle', {
        y: -20,
        duration: 3,
        repeat: -1,
        yoyo: true,
        ease: 'power1.inOut',
        stagger: 0.5
      });
    });

    return () => ctx.revert();
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const navItems = [
    { label: 'Home', href: '#home' },
    { label: 'About', href: '#about' },
    { label: 'Projects', href: '#projects' },
    { label: 'Contact', href: '#contact' },
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer
      ref={footerRef}
      className="relative glass-card mx-6 md:mx-12 lg:mx-24 mb-8 overflow-hidden"
      data-scroll-section
    >
      {/* Floating Particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className={`particle absolute w-2 h-2 bg-primary/30 rounded-full`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      <div className="relative z-10 p-8 md:p-12">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            {/* Brand */}
            <div className="space-y-4">
              <h3 className="text-2xl font-light text-glow">
                Nitesh Gupta
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                Web Developer passionate about creating innovative digital experiences
                with cutting-edge technology.
              </p>
            </div>

            {/* Navigation */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-foreground/90">
                Navigation
              </h4>
              <div className="space-y-2">
                {navItems.map((item) => (
                  <button
                    key={item.label}
                    onClick={() => scrollToSection(item.href)}
                    className="block text-muted-foreground hover:text-primary transition-colors duration-300 text-sm"
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Connect */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-foreground/90">
                Connect
              </h4>
              <div className="flex space-x-4">
                <a
                  href="https://github.com/nitesh124-coder"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="glass p-3 rounded-lg hover:glow transition-all duration-300"
                >
                  <GithubLogo size={20} className="text-foreground/80" />
                </a>
                
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="glass p-3 rounded-lg hover:glow transition-all duration-300"
                >
                  <LinkedinLogo size={20} className="text-foreground/80" />
                </a>
              </div>
              
              <div className="pt-4">
                <a
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-primary transition-colors duration-300 text-sm"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-glass-border pt-8">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
              <div className="flex items-center space-x-2 text-muted-foreground text-sm">
                <span>© 2024 Made with</span>
                <Heart size={16} className="text-primary" />
                <span>by Nitesh Gupta</span>
              </div>
              
              <button
                onClick={scrollToTop}
                className="btn-glass text-sm"
              >
                Back to Top
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;