import React, { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { List, X, GithubLogo, LinkedinLogo } from 'phosphor-react';


const Navigation: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navRef = useRef<HTMLElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const navItems = [
    { label: 'Home', href: '#home' },
    { label: 'About', href: '#about' },
    { label: 'Skills', href: '#skills' },
    { label: 'Projects', href: '#projects' },
    { label: 'Testimonials', href: '#testimonials' },
    { label: 'Contact', href: '#contact' },
  ];

  useEffect(() => {
    // Animate nav in after preloader
    const navAnimation = () => {
      gsap.fromTo(navRef.current,
        { y: -100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power2.out', delay: 4 }
      );
    };

    if (document.body.classList.contains('loaded')) {
      navAnimation();
    } else {
      const checkLoaded = () => {
        if (document.body.classList.contains('loaded')) {
          navAnimation();
          document.removeEventListener('DOMSubtreeModified', checkLoaded);
        }
      };
      // Listen for when body gets 'loaded' class
      setTimeout(navAnimation, 4000);
    }
  }, []);

  const toggleMobileMenu = () => {
    setIsOpen(!isOpen);
    
    if (mobileMenuRef.current) {
      if (!isOpen) {
        gsap.fromTo(mobileMenuRef.current,
          { x: '100%', opacity: 0 },
          { x: '0%', opacity: 1, duration: 0.5, ease: 'power2.out' }
        );
      } else {
        gsap.to(mobileMenuRef.current, {
          x: '100%',
          opacity: 0,
          duration: 0.3,
          ease: 'power2.in'
        });
      }
    }
  };

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsOpen(false);
  };

  return (
    <>
      <nav 
        ref={navRef}
        className="fixed top-6 left-1/2 transform -translate-x-1/2 z-40 opacity-0"
      >
        <div className="nav-glass flex items-center space-x-8">
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => (
              <button
                key={item.label}
                onClick={() => scrollToSection(item.href)}
                className="text-foreground/80 hover:text-foreground transition-all duration-300 hover:text-glow interactive relative group"
              >
                {item.label}
                <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary transition-all duration-300 group-hover:w-full" />
              </button>
            ))}
          </div>

          {/* Theme Toggle & Social Links */}
          <div className="hidden md:flex items-center space-x-4 ml-6 pl-6 border-l border-glass-border">
            
            <a
              href="https://github.com/nitesh124-coder"
              target="_blank"
              rel="noopener noreferrer"
              className="text-foreground/60 hover:text-primary transition-all duration-300 interactive hover:glow"
            >
              <GithubLogo size={20} />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="text-foreground/60 hover:text-primary transition-all duration-300 interactive hover:glow"
            >
              <LinkedinLogo size={20} />
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className="md:hidden text-foreground p-2 interactive hover:glow transition-all duration-300"
          >
            {isOpen ? <X size={24} /> : <List size={24} />}
          </button>
        </div>
      </nav>

      {/* Mobile Menu */}
      {isOpen && (
        <div
          ref={mobileMenuRef}
          className="fixed inset-0 z-50 md:hidden"
          style={{ transform: 'translateX(100%)' }}
        >
          <div className="glass h-full flex flex-col items-center justify-center space-y-8">
            {navItems.map((item, index) => (
              <button
                key={item.label}
                onClick={() => scrollToSection(item.href)}
                className="text-2xl text-foreground/80 hover:text-foreground transition-all duration-300"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {item.label}
              </button>
            ))}
            
            <div className="flex items-center space-x-6 mt-8">
              <a
                href="https://github.com/nitesh124-coder"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground/60 hover:text-primary transition-all duration-300"
              >
                <GithubLogo size={28} />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground/60 hover:text-primary transition-all duration-300"
              >
                <LinkedinLogo size={28} />
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Navigation;