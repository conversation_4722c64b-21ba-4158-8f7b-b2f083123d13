import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Hero: React.FC = () => {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLButtonElement>(null);
  const splineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline({ delay: 2 }); // Reduced delay

    // Only animate if elements exist
    if (titleRef.current && subtitleRef.current && ctaRef.current) {
      tl.fromTo(titleRef.current,
        { opacity: 0, y: 30 },
        { opacity: 1, y: 0, duration: 1, ease: 'power2.out' }
      )
      .fromTo(subtitleRef.current,
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' },
        '-=0.4'
      )
      .fromTo(ctaRef.current,
        { opacity: 0, scale: 0.9 },
        { opacity: 1, scale: 1, duration: 0.6, ease: 'power2.out' },
        '-=0.4'
      );
    }

    if (splineRef.current) {
      gsap.fromTo(splineRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 1, delay: 3 }
      );
    }

    return () => {
      tl.kill();
    };
  }, []);

  const scrollToContact = () => {
    const element = document.querySelector('#contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section
      id="home"
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-between px-6 md:px-12 lg:px-24"
    >
      {/* Left Content */}
      <div className="w-full md:w-1/2 z-10">
        <h1
          ref={titleRef}
          className="hero-title opacity-0 mb-6"
        >
          Hi, I'm<br />
          <span className="text-glow">Nitesh Gupta</span><br />
          Web Developer
        </h1>
        
        <p
          ref={subtitleRef}
          className="hero-subtitle opacity-0 mb-8 max-w-lg"
        >
          Crafting digital experiences that inspire and engage through innovative design and cutting-edge technology.
        </p>
        
        <button
          ref={ctaRef}
          onClick={scrollToContact}
          className="btn-primary opacity-0"
        >
          Hire Me
        </button>
      </div>

      {/* Right Content - Spline 3D */}
      <div
        ref={splineRef}
        className="hidden md:block w-1/2 h-screen opacity-0"
      >
        <iframe
          src="https://my.spline.design/worldplanet-wHfKolcVJtVVCAXIoLc2iVqs/"
          frameBorder="0"
          width="100%"
          height="100%"
          className="rounded-2xl"
        />
      </div>

      {/* Mobile Spline Background */}
      <div className="md:hidden absolute inset-0 opacity-30">
        <iframe
          src="https://my.spline.design/worldplanet-wHfKolcVJtVVCAXIoLc2iVqs/"
          frameBorder="0"
          width="100%"
          height="100%"
        />
      </div>
    </section>
  );
};

export default Hero;