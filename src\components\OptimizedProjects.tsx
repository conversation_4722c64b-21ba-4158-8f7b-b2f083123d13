import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ArrowUpRight, GithubLogo } from 'phosphor-react';
import LazyImage from './LazyImage';

// Import project images
import libraryManagementImg from '../assets/library-management.jpg';
import oralCancerImg from '../assets/oral-cancer-detection.jpg';
import islTranslatorImg from '../assets/isl-translator.jpg';
import gymSupplementsImg from '../assets/gym-supplements.jpg';
import jobResumeImg from '../assets/job-resume-analyzer.jpg';

const OptimizedProjects: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [filter, setFilter] = useState('All');
  const [isLoading, setIsLoading] = useState(true);

  const categories = ['All', 'Web App', 'AI/ML'];

  const projects = useMemo(() => [
    {
      id: 1,
      title: "Online Library Management System",
      description: "Complete web-based library management system with book cataloging, user management, and circulation tracking features",
      image: libraryManagementImg,
      tech: ["HTML", "CSS", "JavaScript", "PHP", "MySQL"],
      category: "Web App",
      github: "https://github.com/nitesh124-coder/ONLINE-LIBRARY-MANAGEMENT-SYSTEM",
      demo: "#",
      featured: true
    },
    {
      id: 2,
      title: "Oral Cancer Detection AI",
      description: "Machine learning model for early detection of oral cancer using advanced image analysis and neural network algorithms",
      image: oralCancerImg,
      tech: ["Python", "TensorFlow", "Jupyter", "OpenCV", "Machine Learning"],
      category: "AI/ML",
      github: "https://github.com/nitesh124-coder/oral-cancer-detection",
      demo: "#",
      featured: true
    },
    {
      id: 3,
      title: "ISL Translator",
      description: "Real-time Indian Sign Language translator using computer vision and gesture recognition technology for accessibility",
      image: islTranslatorImg,
      tech: ["HTML", "CSS", "JavaScript", "Python", "OpenCV"],
      category: "AI/ML",
      github: "https://github.com/nitesh124-coder/isl-translator",
      demo: "#",
      featured: false
    },
    {
      id: 4,
      title: "Gym Supplements Store",
      description: "Modern e-commerce platform for fitness supplements with product catalog, shopping cart, and secure payment integration",
      image: gymSupplementsImg,
      tech: ["HTML", "CSS", "JavaScript", "Bootstrap", "PHP"],
      category: "Web App", 
      github: "https://github.com/nitesh124-coder/gym-supplements",
      demo: "#",
      featured: false
    },
    {
      id: 5,
      title: "Job Resume Analyzer",
      description: "AI-powered resume analysis tool for intelligent job matching, comprehensive skill assessment, and career recommendations",
      image: jobResumeImg,
      tech: ["HTML", "CSS", "JavaScript", "Python", "NLP"],
      category: "AI/ML",
      github: "https://github.com/nitesh124-coder/job-resume-analyzer",
      demo: "#",
      featured: false
    }
  ], []);

  const filteredProjects = useMemo(() => {
    return filter === 'All' 
      ? projects 
      : projects.filter(project => project.category === filter);
  }, [filter, projects]);

  const handleFilterChange = useCallback((category: string) => {
    setFilter(category);
  }, []);

  const handleLinkClick = useCallback((url: string, e: React.MouseEvent) => {
    e.preventDefault();
    if (url !== '#') {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isLoading) return;

    const ctx = gsap.context(() => {
      // Enhanced title animation
      gsap.fromTo(titleRef.current,
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: titleRef.current,
            start: 'top 85%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Optimized staggered animation with better performance
      gsap.fromTo('.project-card',
        { 
          opacity: 0, 
          y: 80, 
          scale: 0.9,
          rotationY: 15
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          rotationY: 0,
          duration: 1,
          ease: 'power2.out',
          stagger: {
            amount: 0.6,
            from: "start"
          },
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Filter animation
      gsap.fromTo('.filter-buttons',
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.filter-buttons',
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    return () => ctx.revert();
  }, [isLoading, filteredProjects]);

  if (isLoading) {
    return (
      <section className="min-h-screen px-6 md:px-12 lg:px-24 py-20 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-secondary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-accent rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </section>
    );
  }

  return (
    <section
      id="projects"
      ref={sectionRef}
      className="min-h-screen px-4 sm:px-6 md:px-12 lg:px-24 py-20"
    >
      <div className="max-w-7xl mx-auto">
        <h2
          ref={titleRef}
          className="text-3xl sm:text-4xl md:text-5xl font-light text-glow text-center mb-12"
        >
          Featured Projects
        </h2>

        {/* Enhanced Filter Buttons */}
        <div className="filter-buttons flex flex-wrap justify-center gap-3 sm:gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => handleFilterChange(category)}
              className={`px-4 sm:px-6 py-2 sm:py-3 rounded-full transition-all duration-300 interactive text-sm sm:text-base ${
                filter === category
                  ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-glow scale-105'
                  : 'glass hover:glow text-foreground/80 hover:text-foreground hover:scale-105'
              }`}
              aria-pressed={filter === category}
            >
              {category}
            </button>
          ))}
        </div>

        <div
          ref={containerRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8"
        >
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className={`project-card group relative overflow-hidden ${
                project.featured ? 'lg:col-span-2' : ''
              }`}
            >
              {/* Enhanced Project Image with Lazy Loading */}
              <div className="relative overflow-hidden rounded-xl mb-6 group-hover:scale-[1.02] transition-transform duration-500">
                <LazyImage
                  src={project.image}
                  alt={project.title}
                  className={`transform group-hover:scale-110 transition-transform duration-700 ${
                    project.featured ? 'h-56 sm:h-64' : 'h-44 sm:h-48'
                  }`}
                  placeholderClassName="flex items-center justify-center"
                />
                
                <div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* Enhanced Featured Badge */}
                {project.featured && (
                  <div className="absolute top-3 sm:top-4 right-3 sm:right-4 px-2 sm:px-3 py-1 bg-gradient-to-r from-accent to-tertiary rounded-full text-xs font-medium backdrop-blur-sm">
                    Featured
                  </div>
                )}

                {/* Enhanced Quick Actions Overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex space-x-3 sm:space-x-4">
                    <button
                      onClick={(e) => handleLinkClick(project.github, e)}
                      className="glass p-2 sm:p-3 rounded-full hover:glow transition-all duration-300 interactive backdrop-blur-md"
                      aria-label={`View ${project.title} on GitHub`}
                    >
                      <GithubLogo size={18} className="text-foreground" />
                    </button>
                    <button
                      onClick={(e) => handleLinkClick(project.demo, e)}
                      className="glass p-2 sm:p-3 rounded-full hover:glow transition-all duration-300 interactive backdrop-blur-md"
                      aria-label={`View ${project.title} demo`}
                    >
                      <ArrowUpRight size={18} className="text-foreground" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Enhanced Project Info */}
              <div className="space-y-4 relative z-10 px-2">
                <div className="flex items-start justify-between gap-3">
                  <h3 className="text-lg sm:text-xl font-medium text-foreground group-hover:text-glow transition-colors duration-300 leading-tight">
                    {project.title}
                  </h3>
                  <span className="text-xs px-2 py-1 glass rounded-full text-primary whitespace-nowrap">
                    {project.category}
                  </span>
                </div>
                
                <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
                  {project.description}
                </p>

                {/* Enhanced Tech Stack */}
                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech) => (
                    <span
                      key={tech}
                      className="px-2 sm:px-3 py-1 text-xs glass rounded-full text-foreground/80 hover:glow transition-all duration-300 interactive"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Enhanced Action Buttons */}
                <div className="flex items-center justify-between pt-4 border-t border-glass-border">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={(e) => handleLinkClick(project.github, e)}
                      className="flex items-center space-x-2 text-foreground/60 hover:text-primary transition-colors duration-300 interactive"
                    >
                      <GithubLogo size={16} />
                      <span className="text-sm">Code</span>
                    </button>
                    
                    <button
                      onClick={(e) => handleLinkClick(project.demo, e)}
                      className="flex items-center space-x-2 text-foreground/60 hover:text-secondary transition-colors duration-300 interactive"
                    >
                      <ArrowUpRight size={16} />
                      <span className="text-sm">Demo</span>
                    </button>
                  </div>
                  
                  <button 
                    className="text-primary hover:text-primary-glow transition-colors duration-300 interactive"
                    aria-label={`Learn more about ${project.title}`}
                  >
                    <ArrowUpRight size={18} />
                  </button>
                </div>
              </div>

              {/* Enhanced Hover Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
            </div>
          ))}
        </div>

        {/* No Results State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg">
              No projects found for "{filter}" category.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default OptimizedProjects;