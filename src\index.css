@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Futuristic Portfolio Design System */

/* Ultra-smooth body styling */
body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  min-height: 100vh;
  overflow-x: hidden;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
:root {
    /* Enhanced Dark Theme with Vibrant Colors */
    --background: 222 84% 2%;
    --foreground: 210 40% 98%;

    /* Premium Neon Gradient Colors */
    --primary: 280 100% 70%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 280 100% 85%;
    --primary-dark: 280 100% 45%;

    --secondary: 190 100% 65%;
    --secondary-foreground: 222 84% 2%;
    --secondary-glow: 190 100% 80%;
    --secondary-dark: 190 100% 40%;

    --accent: 45 100% 70%;
    --accent-foreground: 0 0% 100%;
    --accent-glow: 45 100% 85%;
    --accent-dark: 45 100% 45%;

    --tertiary: 320 100% 65%;
    --tertiary-glow: 320 100% 80%;

    /* Advanced Glass Morphism */
    --glass: 240 10% 10% / 0.08;
    --glass-border: 240 10% 50% / 0.15;
    --glass-hover: 240 10% 15% / 0.12;
    --glass-strong: 240 10% 5% / 0.15;

    /* Card & Surface Colors */
    --card: 222 84% 3%;
    --card-foreground: 210 40% 95%;
    --card-glow: 280 100% 70% / 0.1;

    --popover: 222 84% 2%;
    --popover-foreground: 210 40% 98%;

    /* Text Colors */
    --muted: 217 32% 15%;
    --muted-foreground: 215 20% 70%;

    /* Interactive Elements */
    --border: 217 32% 15%;
    --input: 217 32% 12%;
    --ring: 280 100% 70%;

    /* Destructive */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --radius: 1rem;

    /* Advanced Design Tokens */
    --glow-intensity: 0.6;
    --blur-strength: 20px;
    --animation-speed: 0.4s;
    
    /* Premium Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--accent)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--tertiary)));
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.12), rgba(255,255,255,0.04));
    --gradient-rainbow: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--secondary)), hsl(var(--accent)), hsl(var(--tertiary)));
    --gradient-glow: radial-gradient(circle at center, hsl(var(--primary) / 0.4), transparent 70%);
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--secondary) / 0.1), hsl(var(--accent) / 0.1));
    
    /* Advanced Shadows */
    --shadow-glow: 0 0 60px hsl(var(--primary) / var(--glow-intensity));
    --shadow-glow-lg: 0 0 100px hsl(var(--primary) / 0.8);
    --shadow-glass: 0 16px 40px rgba(0, 0, 0, 0.4);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.5);
    --shadow-floating: 0 20px 60px rgba(0, 0, 0, 0.3);
    
    /* Smooth Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.6s cubic-bezier(0.68, -0.3, 0.32, 1.3);
  }

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-light;
    font-family: 'Inter', system-ui, sans-serif;
    overflow-x: hidden;
    cursor: none; /* Hide default cursor for custom cursor */
  }

  /* Custom Cursor */
  .custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    box-shadow: var(--shadow-glow);
  }

  .custom-cursor::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
  }

  .custom-cursor.hover {
    transform: scale(1.5);
  }

  .custom-cursor.hover::before {
    transform: translate(-50%, -50%) scale(2);
    background: rgba(255, 255, 255, 0.05);
  }

  .glass {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(var(--blur-strength));
    border: 1px solid rgba(255, 255, 255, 0.12);
    transition: var(--transition-smooth);
  }

  .glass:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  .glow {
    filter: drop-shadow(var(--shadow-glow));
    transition: filter var(--transition-smooth);
  }

  .glow:hover {
    filter: drop-shadow(var(--shadow-glow-lg));
  }

  .text-glow {
    background: var(--gradient-rainbow);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px hsl(var(--primary) / 0.6);
    animation: text-shimmer 3s ease-in-out infinite;
    background-size: 200% 100%;
  }
}

@layer components {
  /* Always Visible Custom Cursor */
  .custom-cursor {
    position: fixed !important;
    width: 20px;
    height: 20px;
    background: rgba(147, 51, 234, 0.9) !important;
    border: 2px solid rgba(147, 51, 234, 1) !important;
    border-radius: 50%;
    pointer-events: none !important;
    z-index: 99999 !important;
    transform: translate(-50%, -50%) !important;
    transition: all 0.1s ease !important;
    backdrop-filter: blur(2px);
    box-shadow: 0 0 10px rgba(147, 51, 234, 0.5) !important;
    top: 0 !important;
    left: 0 !important;
  }

  .custom-cursor.hover {
    width: 40px !important;
    height: 40px !important;
    background: rgba(6, 182, 212, 0.8) !important;
    border-color: rgba(6, 182, 212, 1) !important;
    border-width: 3px !important;
    box-shadow: 0 0 15px rgba(6, 182, 212, 0.6) !important;
  }

  /* Force hide default cursor everywhere */
  *, *:before, *:after {
    cursor: none !important;
  }

  html, body {
    cursor: none !important;
  }

  /* Simple Preloader */
  .preloader {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: #0a0a0a !important;
    z-index: 10000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-direction: column !important;
  }

  /* Contact Form Styling */
  .input-glass {
    @apply w-full px-4 py-3 glass rounded-lg border border-white/10 bg-white/5;
    @apply text-foreground placeholder-muted-foreground;
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
    @apply transition-all duration-300;
    backdrop-filter: blur(10px);
  }

  .input-glass:focus {
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  }

  .btn-primary {
    @apply px-8 py-4 text-primary-foreground font-medium rounded-lg;
    @apply transition-all duration-300 transform hover:scale-105;
    @apply shadow-lg hover:shadow-glow;
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, hsl(var(--primary-dark)), hsl(var(--secondary-dark)));
    box-shadow: var(--shadow-glow);
  }

  /* Hero Section */
  .hero-title {
    @apply text-6xl md:text-8xl font-light tracking-tight;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hero-subtitle {
    @apply text-xl md:text-2xl font-light opacity-80;
  }

  /* Glassmorphic Cards */
  .glass-card {
    @apply glass rounded-2xl p-6 transition-all duration-500;
    box-shadow: var(--shadow-glass);
  }

  .glass-card:hover {
    @apply transform scale-105;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: var(--shadow-glow), var(--shadow-glass);
  }

  /* Buttons */
  .btn-primary {
    @apply px-8 py-4 rounded-full font-medium transition-all duration-300;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-glow);
  }

  .btn-primary:hover {
    @apply transform scale-105;
    box-shadow: 0 0 60px hsl(var(--primary) / 0.6);
  }

  .btn-glass {
    @apply glass px-6 py-3 rounded-full font-medium transition-all duration-300;
  }

  .btn-glass:hover {
    @apply transform scale-105;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-glow);
  }

  /* Floating Elements - Simplified */
  .floating-orb {
    @apply absolute rounded-full opacity-10;
    background: radial-gradient(circle, hsl(var(--primary) / 0.3), transparent 70%);
    animation: float-simple 8s ease-in-out infinite;
  }

  /* Form Elements */
  .input-glass {
    @apply glass px-4 py-3 rounded-lg w-full text-foreground placeholder-muted-foreground;
    transition: var(--transition-smooth);
  }

  .input-glass:focus {
    @apply outline-none;
    border-color: hsl(var(--primary));
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  /* Navigation */
  .nav-glass {
    @apply glass px-6 py-4 rounded-full;
    backdrop-filter: blur(20px);
  }

  /* Project Cards */
  .project-card {
    @apply glass-card relative overflow-hidden cursor-pointer;
    min-height: 300px;
  }

  .project-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 opacity-0 transition-opacity duration-300;
  }

  .project-card:hover::before {
    @apply opacity-100;
  }

  /* Skills Grid */
  .skill-icon {
    @apply glass p-4 rounded-xl flex items-center justify-center;
    width: 80px;
    height: 80px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
  }

  .skill-icon:hover {
    @apply transform scale-110 -translate-y-1;
    box-shadow: var(--shadow-glow);
    border-color: hsl(var(--primary) / 0.5);
  }

  /* Enhanced smooth scrolling and interactions */
  .interactive {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
  }

  .interactive:hover {
    transform: translateY(-2px);
  }

  .glass {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
  }

  .glass:hover {
    backdrop-filter: blur(calc(var(--blur-strength) + 5px));
    box-shadow: var(--shadow-glow);
  }
}

@layer utilities {
  .blur-in {
    filter: blur(10px);
    opacity: 0;
  }

  .blur-out {
    filter: blur(0px);
    opacity: 1;
  }
}

/* Advanced Animation Keyframes */
@keyframes float-simple {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.4);
  }
  50% {
    box-shadow: 0 0 60px hsl(var(--primary) / 0.8);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
    opacity: 0.5;
  }
}

@keyframes magnetic-pull {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(var(--mouse-x, 0), var(--mouse-y, 0));
  }
}

/* Light Theme Variables */
.light-theme {
  --background: 220 20% 97%;
  --foreground: 222 84% 5%;
  --card: 220 20% 95%;
  --primary: 280 100% 60%;
  --secondary: 190 100% 55%;
  --accent: 45 100% 60%;
  --muted-foreground: 215 20% 40%;
  --glass: 240 10% 90% / 0.1;
}

/* Utility Classes */
.interactive {
  cursor: pointer;
  transition: var(--transition-smooth);
}

.interactive:hover {
  transform: translateY(-2px);
}

.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 40px hsl(var(--primary) / 0.6);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Locomotive Scroll */
html.has-scroll-smooth {
  overflow: hidden;
}

html.has-scroll-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.has-scroll-smooth body {
  overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
  min-height: 100vh;
}

[data-scroll-direction="horizontal"] [data-scroll-container] {
  height: 100vh;
  display: inline-block;
  white-space: nowrap;
}

[data-scroll-direction="horizontal"] [data-scroll-section] {
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  height: 100%;
}

.c-scrollbar {
  position: absolute;
  right: 0;
  top: 0;
  width: 11px;
  height: 100%;
  transform-origin: center right;
  transition: transform 0.3s, opacity 0.3s;
  opacity: 0;
}

.c-scrollbar:hover {
  transform: scaleX(1.45);
}

.c-scrollbar.c-scrollbar_show {
  opacity: 1;
}

[data-scroll-direction="horizontal"] .c-scrollbar {
  width: 100%;
  height: 10px;
  top: auto;
  bottom: 0;
  transform: scaleY(1);
}

[data-scroll-direction="horizontal"] .c-scrollbar:hover {
  transform: scaleY(1.3);
}

.c-scrollbar_thumb {
  position: absolute;
  top: 0;
  right: 0;
  background-color: hsl(var(--primary));
  opacity: 0.5;
  width: 7px;
  border-radius: 10px;
  margin: 2px;
  cursor: -webkit-grab;
  cursor: grab;
}

.c-scrollbar_thumb:hover {
  background-color: hsl(var(--primary-glow));
  opacity: 0.8;
}

[data-scroll-direction="horizontal"] .c-scrollbar_thumb {
  right: auto;
  bottom: 0;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}