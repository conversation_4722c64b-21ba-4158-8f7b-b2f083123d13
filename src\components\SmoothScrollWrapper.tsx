import React, { useState, useRef, useEffect } from 'react';

interface SmoothScrollProps {
  children: React.ReactNode;
}

const SmoothScrollWrapper: React.FC<SmoothScrollProps> = ({ children }) => {
  const [lenis, set<PERSON>enis] = useState<any>(null);
  const requestRef = useRef<number>();

  useEffect(() => {
    // Enhanced smooth scrolling with better performance
    const smoothScrollFallback = () => {
      const style = document.createElement('style');
      style.textContent = `
        html {
          scroll-behavior: smooth;
          scroll-padding-top: 100px;
        }
        
        body {
          overscroll-behavior: none;
        }
        
        /* Optimize all transitions for better performance */
        * {
          backface-visibility: hidden;
          transform: translateZ(0);
        }
        
        /* Enhanced smooth animations */
        .interactive {
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          will-change: transform;
        }
        
        .interactive:hover {
          transform: translateY(-3px) scale(1.02);
        }
        
        .glass {
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          will-change: transform, backdrop-filter, box-shadow;
        }
        
        .glass:hover {
          transform: translateY(-2px);
          backdrop-filter: blur(25px);
          box-shadow: var(--shadow-glow);
        }
        
        /* Enhanced button animations */
        button.interactive {
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          transform-origin: center;
        }
        
        button.interactive:hover {
          transform: translateY(-2px) scale(1.05);
        }
        
        button.interactive:active {
          transform: translateY(0) scale(0.98);
          transition-duration: 0.1s;
        }
        
        /* Smooth project card animations */
        .project-card {
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          transform-origin: center;
        }
        
        .project-card:hover {
          transform: translateY(-8px) scale(1.02);
        }
        
        /* Enhanced navigation smoothness */
        nav a {
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          position: relative;
        }
        
        nav a::after {
          content: '';
          position: absolute;
          width: 0;
          height: 2px;
          bottom: -4px;
          left: 0;
          background: var(--gradient-primary);
          transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        nav a:hover::after {
          width: 100%;
        }
        
        /* Smooth scroll snap for sections */
        section {
          scroll-snap-align: start;
        }
        
        /* Performance optimizations */
        @media (prefers-reduced-motion: reduce) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `;
      
      document.head.appendChild(style);
      
      return () => {
        if (document.head.contains(style)) {
          document.head.removeChild(style);
        }
      };
    };

    const cleanup = smoothScrollFallback();

    // Enhanced scroll performance
    let ticking = false;
    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', onScroll, { passive: true });

    return () => {
      cleanup();
      window.removeEventListener('scroll', onScroll);
    };
  }, []);

  return <>{children}</>;
};

export default SmoothScrollWrapper;