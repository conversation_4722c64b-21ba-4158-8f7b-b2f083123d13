import React, { useEffect, useRef, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ArrowUpRight, GithubLogo } from 'phosphor-react';

// Import project images
import libraryManagementImg from '../assets/library-management.jpg';
import oralCancerImg from '../assets/oral-cancer-detection.jpg';
import islTranslatorImg from '../assets/isl-translator.jpg';
import gymSupplementsImg from '../assets/gym-supplements.jpg';
import jobResumeImg from '../assets/job-resume-analyzer.jpg';

const Projects: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [filter, setFilter] = useState('All');

  const categories = ['All', 'Web App', 'AI/ML'];

  const projects = [
    {
      id: 1,
      title: "Online Library Management System",
      description: "Complete web-based library management system with book cataloging, user management, and circulation tracking",
      image: libraryManagementImg,
      tech: ["HTML", "CSS", "JavaScript", "PHP", "MySQL"],
      category: "Web App",
      github: "https://github.com/nitesh124-coder/ONLINE-LIBRARY-MANAGEMENT-SYSTEM",
      demo: "#",
      featured: true
    },
    {
      id: 2,
      title: "Oral Cancer Detection AI",
      description: "Machine learning model for early detection of oral cancer using image analysis and neural networks",
      image: oralCancerImg,
      tech: ["Python", "TensorFlow", "Jupyter", "OpenCV", "Machine Learning"],
      category: "AI/ML",
      github: "https://github.com/nitesh124-coder/oral-cancer-detection",
      demo: "#",
      featured: true
    },
    {
      id: 3,
      title: "ISL Translator",
      description: "Real-time Indian Sign Language translator using computer vision and gesture recognition technology",
      image: islTranslatorImg,
      tech: ["HTML", "CSS", "JavaScript", "Python", "OpenCV"],
      category: "AI/ML",
      github: "https://github.com/nitesh124-coder/isl-translator",
      demo: "#",
      featured: false
    },
    {
      id: 4,
      title: "Gym Supplements Store",
      description: "E-commerce platform for fitness supplements with product catalog, shopping cart, and payment integration",
      image: gymSupplementsImg,
      tech: ["HTML", "CSS", "JavaScript", "Bootstrap", "PHP"],
      category: "Web App", 
      github: "https://github.com/nitesh124-coder/gym-supplements",
      demo: "#",
      featured: false
    },
    {
      id: 5,
      title: "Job Resume Analyzer",
      description: "AI-powered resume analysis tool for job matching, skill assessment, and career recommendations",
      image: jobResumeImg,
      tech: ["HTML", "CSS", "JavaScript", "Python", "NLP"],
      category: "AI/ML",
      github: "https://github.com/nitesh124-coder/job-resume-analyzer",
      demo: "#",
      featured: false
    }
  ];

  const filteredProjects = filter === 'All' 
    ? projects 
    : projects.filter(project => project.category === filter);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: titleRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Projects staggered animation
      gsap.fromTo('.project-card',
        { opacity: 0, y: 100, scale: 0.8 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: 'power2.out',
          stagger: 0.2,
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    return () => ctx.revert();
  }, []);

  return (
    <section
      id="projects"
      ref={sectionRef}
      className="min-h-screen px-6 md:px-12 lg:px-24 py-20"
      data-scroll-section
    >
      <div className="max-w-7xl mx-auto">
        <h2
          ref={titleRef}
          className="text-4xl md:text-5xl font-light text-glow text-center mb-8"
        >
          Featured Projects
        </h2>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`px-6 py-3 rounded-full transition-all duration-300 interactive ${
                filter === category
                  ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-glow'
                  : 'glass hover:glow text-foreground/80 hover:text-foreground'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        <div
          ref={containerRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className={`project-card group relative overflow-hidden ${
                project.featured ? 'lg:col-span-2' : ''
              }`}
            >
              {/* Project Image */}
              <div className="relative overflow-hidden rounded-xl mb-6 group-hover:scale-105 transition-transform duration-500">
                <img
                  src={project.image}
                  alt={project.title}
                  className={`w-full object-cover transform group-hover:scale-110 transition-transform duration-500 ${
                    project.featured ? 'h-64' : 'h-48'
                  }`}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* Featured Badge */}
                {project.featured && (
                  <div className="absolute top-4 right-4 px-3 py-1 bg-gradient-to-r from-accent to-tertiary rounded-full text-xs font-medium">
                    Featured
                  </div>
                )}

                {/* Quick Actions Overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex space-x-4">
                    <a
                      href={project.github}
                      className="glass p-3 rounded-full hover:glow transition-all duration-300 interactive"
                    >
                      <GithubLogo size={20} className="text-foreground" />
                    </a>
                    <a
                      href={project.demo}
                      className="glass p-3 rounded-full hover:glow transition-all duration-300 interactive"
                    >
                      <ArrowUpRight size={20} className="text-foreground" />
                    </a>
                  </div>
                </div>
              </div>

              {/* Project Info */}
              <div className="space-y-4 relative z-10">
                <div className="flex items-start justify-between">
                  <h3 className="text-xl font-medium text-foreground group-hover:text-glow transition-colors duration-300">
                    {project.title}
                  </h3>
                  <span className="text-xs px-2 py-1 glass rounded-full text-primary">
                    {project.category}
                  </span>
                </div>
                
                <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
                  {project.description}
                </p>

                {/* Tech Stack */}
                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech) => (
                    <span
                      key={tech}
                      className="px-3 py-1 text-xs glass rounded-full text-foreground/80 hover:glow transition-all duration-300 interactive"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Action Buttons - Bottom */}
                <div className="flex items-center justify-between pt-4 border-t border-glass-border">
                  <div className="flex items-center space-x-4">
                    <a
                      href={project.github}
                      className="flex items-center space-x-2 text-foreground/60 hover:text-primary transition-colors duration-300 interactive"
                    >
                      <GithubLogo size={18} />
                      <span className="text-sm">Code</span>
                    </a>
                    
                    <a
                      href={project.demo}
                      className="flex items-center space-x-2 text-foreground/60 hover:text-secondary transition-colors duration-300 interactive"
                    >
                      <ArrowUpRight size={18} />
                      <span className="text-sm">Demo</span>
                    </a>
                  </div>
                  
                  <button className="text-primary hover:text-primary-glow transition-colors duration-300 interactive">
                    <ArrowUpRight size={20} />
                  </button>
                </div>
              </div>

              {/* Hover Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;