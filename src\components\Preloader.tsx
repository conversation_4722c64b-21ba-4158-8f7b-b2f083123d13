import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Preloader: React.FC = () => {
  const preloaderRef = useRef<HTMLDivElement>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Ensure preloader is visible
    if (preloaderRef.current) {
      preloaderRef.current.style.display = 'flex';
    }

    // Your exact GSAP animation specification
    gsap.to(progressBarRef.current, {
      width: "100%",
      duration: 2,
      ease: "power2.out",
      onComplete: () => {
        gsap.to(preloaderRef.current, {
          opacity: 0,
          scale: 0.9,
          duration: 1,
          onComplete: () => {
            if (preloaderRef.current) {
              preloaderRef.current.style.display = "none";
            }
            // Fade in main content
            gsap.to(".main-content", {
              opacity: 1,
              duration: 1,
              ease: "power2.out"
            });
          }
        });
      }
    });

  }, []);

  return (
    <div 
      ref={preloaderRef}
      className="preloader"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: '#0a0a0a',
        zIndex: 10000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: '3rem'
      }}
    >
      {/* Hello World Text */}
      <div 
        style={{
          fontSize: 'clamp(3rem, 8vw, 6rem)',
          fontWeight: '100',
          color: '#ffffff',
          letterSpacing: '0.3em',
          textAlign: 'center',
          background: 'linear-gradient(45deg, #9333ea, #06b6d4, #10b981)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}
      >
        hello world
      </div>

      {/* Progress Bar Container */}
      <div 
        style={{
          width: '400px',
          height: '4px',
          backgroundColor: 'rgba(255,255,255,0.1)',
          borderRadius: '2px',
          overflow: 'hidden'
        }}
      >
        <div 
          ref={progressBarRef}
          className="progress-bar"
          style={{
            width: '0%',
            height: '100%',
            background: 'linear-gradient(90deg, #9333ea, #06b6d4, #10b981)',
            borderRadius: '2px',
            boxShadow: '0 0 20px rgba(147, 51, 234, 0.5)'
          }}
        />
      </div>
    </div>
  );
};

export default Preloader;